{"G:\\aldallah_be\\models\\Conversation.js": {"path": "G:\\aldallah_be\\models\\Conversation.js", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 18}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 17}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 18}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 5}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 12}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 18}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 18}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 4}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 16}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 18}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 18}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 11}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 43}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 18}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 12}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 19}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 16}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 5}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 4}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 25}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 0}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 68}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1}, "branchMap": {}, "b": {}, "fnMap": {}, "f": {}}, "G:\\aldallah_be\\models\\Message.js": {"path": "G:\\aldallah_be\\models\\Message.js", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 43}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 17}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 24}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 18}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 4}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 11}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 41}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 17}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 18}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 4}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 12}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 17}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 18}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 4}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 16}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 17}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 59}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 19}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 4}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 19}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 13}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 21}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 22}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 19}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 4}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 12}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 41}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 16}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 5}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 17}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 41}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 16}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 5}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 14}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 18}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 18}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 3}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 25}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 0}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 58}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1}, "branchMap": {}, "b": {}, "fnMap": {}, "f": {}}, "G:\\aldallah_be\\models\\Step2.Model.js": {"path": "G:\\aldallah_be\\models\\Step2.Model.js", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 38}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 40}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 3}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 16}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 19}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 21}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 6}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 14}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 19}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 21}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 6}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 18}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 19}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 21}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 6}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 12}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 19}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 21}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 6}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 47}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 44}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 12}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 19}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 6}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 53}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 27}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 16}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 21}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 21}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 7}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 16}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 18}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 24}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 7}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 16}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 20}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 20}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 6}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 4}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 22}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 2}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 0}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 54}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1}, "branchMap": {}, "b": {}, "fnMap": {}, "f": {}}}